#!/bin/bash

# Network scan for *************/24
echo "Scanning *************/24 network..."

# Ping sweep to find active hosts
for i in {1..254}; do
    ping -c 1 -W 1 102.207.191.$i > /dev/null 2>&1 && echo "102.207.191.$i is up"
done

# SNMP discovery for devices that respond
echo -e "\nTesting SNMP on discovered hosts..."
for i in {1..254}; do
    if ping -c 1 -W 1 102.207.191.$i > /dev/null 2>&1; then
        # Test common SNMP communities
        for community in public private; do
            snmpget -v2c -c $community 102.207.191.$i *******.*******.0 > /dev/null 2>&1
            if [ $? -eq 0 ]; then
                echo "102.207.191.$i responds to SNMP community: $community"
            fi
        done
    fi
done