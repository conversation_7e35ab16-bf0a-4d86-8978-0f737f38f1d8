MEMORY_LIMIT=256M
MAX_INPUT_VARS=1000
UPLOAD_MAX_SIZE=16M
OPCACHE_MEM_SIZE=128
REAL_IP_FROM=0.0.0.0/32
REAL_IP_HEADER=X-Forwarded-For
LOG_IP_VAR=remote_addr

CACHE_DRIVER=redis
SESSION_DRIVER=redis
REDIS_HOST=redis

LIBRENMS_SNMP_COMMUNITY=librenmsdocker

LIBRENMS_WEATHERMAP=false
LIBRENMS_WEATHERMAP_SCHEDULE=*/5 * * * *

# SMTP Configuration for ZeptoMail
MAIL_DRIVER=smtp
MAIL_HOST=smtp.zeptomail.com
MAIL_PORT=587
MAIL_USERNAME=emailapikey
MAIL_PASSWORD=wSsVR61wqRWjDq1/nWepJuxpkVUDDwygRkQsiVCj7yf7F6zA8sc8xBGfBFCnTvQXRW9vFmMWpLl4nkgB1DQPj9svzQ4EDyiF9mqRe1U4J3x17qnvhDzCXm1ZlRGIK4kMzgpvmWllEs8m+g==
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="LibreNMS Alerts"
